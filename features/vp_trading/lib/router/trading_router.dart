import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:vp_trading/screen/place_order/main/trading_order_main_screen.dart';
import 'package:vp_trading/screen/sell_portfolio/sell_portfolio_screen.dart';

enum TradingRouter {
  placeOrder('/placeOrder'),
  sellPortfolio('/sellPortfolio');

  final String routeName;

  const TradingRouter(this.routeName);
}

List<RouteBase> tradingRouter() {
  return [
    GoRoute(
      path: TradingRouter.placeOrder.routeName,
      pageBuilder:
          (context, state) => MaterialPage(
            key: state.pageKey,
            fullscreenDialog: true,
            child: TradingOrderMainScreen(
              argument:
                  state.extra as PlaceOrderArgument? ?? PlaceOrderArgument(),
            ),
          ),
    ),
    GoRoute(
      path: TradingRouter.sellPortfolio.routeName,
      builder: (context, state) => const SellPortfolioScreen(),
    ),
  ];
}
