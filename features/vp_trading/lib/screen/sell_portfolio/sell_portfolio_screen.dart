import 'package:flutter/material.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/widget/appbar/appbar_view.dart';
import 'package:vp_design_system/widget/scaffold/scaffold_view.dart';
import 'package:vp_design_system/widget/tabbar/tabbar_view.dart';

class SellPortfolioScreen extends StatelessWidget {
  const SellPortfolioScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return VPScaffold(
      appBar: VPAppBar.layer(
        backgroundColor: vpColor.backgroundElevation0,
        title: "<PERSON><PERSON> nhanh danh mục",
      ),
      body: _buildTabBarView(),
    );
  }

  _buildTabBarView() {
    return DefaultTabController(
      length: 2,
      child: Column(
        children: [
          ColoredBox(
            color: vpColor.backgroundElevation0,
            child: VPTabBar(tabs: [Tab(text: "<PERSON><PERSON><PERSON>ờ<PERSON>"), Tab(text: "<PERSON><PERSON> quỹ")]),
          ),
          Expanded(
            child: Tab<PERSON><PERSON>Vie<PERSON>(children: [Text("Mã thường"), Text("Mã ký quỹ")]),
          ),
        ],
      ),
    );
  }
}
