import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_trading/cubit/sell_portfolio/sell_portfolio_cubit.dart';
import 'package:vp_trading/cubit/sell_portfolio/sell_portfolio_state.dart';

extension VPPopupButtonExt on VPPopup {
  VPPopup btnCancelBottomSelection(String title) => copyWith(
    button: Builder(
      builder:
          (context) => VpsButton.primaryMedium(
            title: title,
            onPressed: Navigator.of(context).pop,
            alignment: Alignment.center,
          ),
    ),
  );
}

class SellPortfolioScreen extends StatefulWidget {
  const SellPortfolioScreen({super.key});

  @override
  State<SellPortfolioScreen> createState() => _SellPortfolioScreenState();
}

class _SellPortfolioScreenState extends State<SellPortfolioScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late SellPortfolioCubit _cubit;
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _cubit = SellPortfolioCubit();
    _cubit.fetchHoldingPortfolio();

    _tabController.addListener(() {
      if (_tabController.indexIsChanging) {
        final accountType =
            _tabController.index == 0 ? AccountType.normal : AccountType.margin;
        _cubit.updateAccountType(accountType);
      }
    });

    _searchController.addListener(() {
      _cubit.updateSearchQuery(_searchController.text);
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    _cubit.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: _cubit,
      child: VPScaffold(
        appBar: VPAppBar.layer(
          backgroundColor: context.colors.backgroundElevation0,
          title: "Bán nhanh danh mục",
        ),
        body: _buildBody(),
      ),
    );
  }

  Widget _buildBody() {
    return Column(
      children: [
        _buildTabBar(),
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [_buildTabContent(), _buildTabContent()],
          ),
        ),
        _buildActionBar(),
      ],
    );
  }

  Widget _buildTabBar() {
    return Container(
      color: context.colors.backgroundElevation0,
      child: VPTabBar(
        controller: _tabController,
        tabs: const [Tab(text: "Thường"), Tab(text: "Ký quỹ")],
      ),
    );
  }

  Widget _buildTabContent() {
    return Column(
      children: [
        _buildSearchField(),
        Expanded(
          child: BlocBuilder<SellPortfolioCubit, SellPortfolioState>(
            builder: (context, state) {
              if (state.apiStatus.isLoading) {
                return const Center(child: CircularProgressIndicator());
              }

              if (state.apiStatus.isError) {
                return Center(
                  child: Text(
                    state.errorMessage ?? 'Có lỗi xảy ra',
                    style: context.textStyle.body14?.copyWith(
                      color: context.colors.textAccentRed,
                    ),
                  ),
                );
              }

              return _buildStockList(state);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildSearchField() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      color: context.colors.backgroundElevation0,
      child: VPTextField.medium(
        controller: _searchController,
        hintText: "Tìm mã CK",
        prefixIcon: (color) => Icon(Icons.search, color: color, size: 24),
      ),
    );
  }

  Widget _buildStockList(SellPortfolioState state) {
    final filteredItems = state.filteredStockItems;

    if (filteredItems.isEmpty) {
      return const Center(child: Text("Không có kết quả phù hợp"));
    }

    return ListView.builder(
      itemCount: filteredItems.length + 1, // +1 for header
      itemBuilder: (context, index) {
        if (index == 0) {
          return _buildListHeader(state);
        }

        final item = filteredItems[index - 1];
        return _buildStockItem(item);
      },
    );
  }

  Widget _buildListHeader(SellPortfolioState state) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: context.colors.backgroundElevation0,
        border: Border(
          bottom: BorderSide(color: context.colors.strokeNormal, width: 1),
        ),
      ),
      child: Row(
        children: [
          // Select all checkbox
          VPCheckBoxView(
            status:
                state.isSelectAll
                    ? CheckBoxStatus.checked
                    : CheckBoxStatus.uncheck,
            onStatusChanged: (status) => _cubit.toggleSelectAll(),
          ),
          const SizedBox(width: 8),
          // "Tất cả" label and controls
          Expanded(
            flex: 2,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "Mã CK",
                  style: context.textStyle.captionRegular?.copyWith(
                    color: context.colors.textPrimary,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  "Tất cả",
                  style: context.textStyle.subtitle14?.copyWith(
                    color: context.colors.textPrimary,
                  ),
                ),
              ],
            ),
          ),
          // Quantity percentage dropdown
          Expanded(
            flex: 3,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  "KL bán",
                  style: context.textStyle.captionRegular?.copyWith(
                    color: context.colors.textPrimary,
                  ),
                ),
                const SizedBox(height: 4),
                GestureDetector(
                  onTap: () => _showPercentageModal(),
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      border: Border.all(color: context.colors.strokeNormal),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          "${state.globalQuantityPercent}%",
                          style: context.textStyle.body14,
                        ),
                        const SizedBox(width: 4),
                        Icon(
                          Icons.keyboard_arrow_down,
                          size: 16,
                          color: context.colors.textPrimary,
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          // Price type dropdown
          Expanded(
            flex: 3,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  "Giá đặt bán",
                  style: context.textStyle.captionRegular?.copyWith(
                    color: context.colors.textPrimary,
                  ),
                ),
                const SizedBox(height: 4),
                GestureDetector(
                  onTap: () => _showPriceTypeModal(),
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      border: Border.all(color: context.colors.strokeNormal),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          state.globalPriceType == PriceType.lo
                              ? "Giá LO"
                              : "Giá thị trường",
                          style: context.textStyle.body14,
                        ),
                        const SizedBox(width: 4),
                        Icon(
                          Icons.keyboard_arrow_down,
                          size: 16,
                          color: context.colors.textPrimary,
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStockItem(SellPortfolioStockItem item) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: context.colors.backgroundElevation0,
        border: Border(
          bottom: BorderSide(color: context.colors.strokeNormal, width: 1),
        ),
      ),
      child: Row(
        children: [
          // Checkbox
          VPCheckBoxView(
            status:
                item.isSelected
                    ? CheckBoxStatus.checked
                    : CheckBoxStatus.uncheck,
            onStatusChanged:
                (status) =>
                    _cubit.toggleStockSelection(item.stock.symbol ?? ''),
          ),
          const SizedBox(width: 8),
          // Stock symbol
          Expanded(
            flex: 2,
            child: Text(
              item.stock.symbol ?? '',
              style: context.textStyle.body14?.copyWith(
                fontWeight: FontWeight.w600,
                color: vpColor.textPrimary,
              ),
            ),
          ),
          // Quantity input with +/- buttons
          Expanded(
            flex: 3,
            child: Container(
              decoration: BoxDecoration(
                border: Border.all(color: context.colors.strokeNormal),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  // Minus button
                  GestureDetector(
                    onTap: () {
                      final newQuantity = (item.quantity - 100).clamp(
                        0,
                        item.stock.trade?.toInt() ?? 0,
                      );
                      _cubit.updateStockQuantity(
                        item.stock.symbol ?? '',
                        newQuantity,
                      );
                    },
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      child: Icon(
                        Icons.remove,
                        size: 16,
                        color: context.colors.textPrimary,
                      ),
                    ),
                  ),
                  // Quantity text
                  Expanded(
                    child: Text(
                      "${item.quantity}",
                      textAlign: TextAlign.center,
                      style: context.textStyle.body14,
                    ),
                  ),
                  // Plus button
                  GestureDetector(
                    onTap: () {
                      final newQuantity = (item.quantity + 100).clamp(
                        0,
                        item.stock.trade?.toInt() ?? 0,
                      );
                      _cubit.updateStockQuantity(
                        item.stock.symbol ?? '',
                        newQuantity,
                      );
                    },
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      child: Icon(
                        Icons.add,
                        size: 16,
                        color: context.colors.textPrimary,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(width: 8),
          // Price input with +/- buttons
          Expanded(
            flex: 3,
            child: Container(
              decoration: BoxDecoration(
                border: Border.all(color: context.colors.strokeNormal),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  // Minus button
                  GestureDetector(
                    onTap: () {
                      final newPrice = (item.price - 0.05).clamp(
                        0.0,
                        double.infinity,
                      );
                      _cubit.updateStockPrice(
                        item.stock.symbol ?? '',
                        newPrice,
                      );
                    },
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      child: Icon(
                        Icons.remove,
                        size: 16,
                        color: context.colors.textPrimary,
                      ),
                    ),
                  ),
                  // Price text
                  Expanded(
                    child: Text(
                      item.price.toStringAsFixed(2),
                      textAlign: TextAlign.center,
                      style: context.textStyle.body14,
                    ),
                  ),
                  // Plus button
                  GestureDetector(
                    onTap: () {
                      final newPrice = item.price + 0.05;
                      _cubit.updateStockPrice(
                        item.stock.symbol ?? '',
                        newPrice,
                      );
                    },
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      child: Icon(
                        Icons.add,
                        size: 16,
                        color: context.colors.textPrimary,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionBar() {
    return BlocBuilder<SellPortfolioCubit, SellPortfolioState>(
      builder: (context, state) {
        return VPBottomActionView.contentAndButton(
          textButton: "Bán danh mục (${state.selectedStockCount})",
          onPressed: state.canSubmit ? () => _cubit.submitSellOrders() : null,
          disableButton: !state.canSubmit,
          leftView: Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: context.colors.backgroundAccentYellow,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.warning,
                  color: context.colors.iconAccentYellow,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    "Quý khách xác nhận đồng ý đặt lệnh Bán nhiều mã Cổ phiếu trong danh mục nắm giữ",
                    style: context.textStyle.captionRegular?.copyWith(
                      color: context.colors.textPrimary,
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _showPercentageModal() {
    VPPopup.bottomAction(
      items: [
        BottomSheetItemWidget(
          text: "25%",
          onTap: () {
            _cubit.updateGlobalQuantityPercent(25);
            Navigator.pop(context);
          },
        ),
        BottomSheetItemWidget(
          text: "50%",
          onTap: () {
            _cubit.updateGlobalQuantityPercent(50);
            Navigator.pop(context);
          },
        ),
        BottomSheetItemWidget(
          text: "75%",
          onTap: () {
            _cubit.updateGlobalQuantityPercent(75);
            Navigator.pop(context);
          },
        ),
        BottomSheetItemWidget(
          text: "100%",
          onTap: () {
            _cubit.updateGlobalQuantityPercent(100);
            Navigator.pop(context);
          },
        ),
      ],
    ).btnCancelBottomSelection('Hủy').showAction(context);
  }

  void _showPriceTypeModal() {
    VPPopup.bottomAction(
      items: [
        BottomSheetItemWidget(
          text: "Giá LO",
          onTap: () {
            _cubit.updateGlobalPriceType(PriceType.lo);
            Navigator.pop(context);
          },
        ),
        BottomSheetItemWidget(
          text: "Giá thị trường",
          onTap: () {
            _cubit.updateGlobalPriceType(PriceType.market);
            Navigator.pop(context);
          },
        ),
      ],
    ).btnCancelBottomSelection('Hủy').showAction(context);
  }
}
