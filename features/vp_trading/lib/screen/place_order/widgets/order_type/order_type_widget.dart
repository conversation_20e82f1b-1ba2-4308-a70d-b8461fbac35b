import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_trading/cubit/place_order/place_order/place_order_cubit.dart';
import 'package:vp_trading/router/trading_router.dart';

import 'select_order_type_bottom_sheet.dart';

class OrderTypeWidget extends StatelessWidget {
  const OrderTypeWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          Expanded(
            child: VPDropdownView.large(
              value: 'Lệnh thường',
              onTap: () async {
                chooseOrderType(
                  context,
                  context.read<PlaceOrderCubit>().state.action,
                  false,
                );
              },
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: VpsButton.secondarySmall(
              onPressed: () {
                context.push(TradingRouter.sellPortfolio.routeName);
              },
              title: "<PERSON><PERSON>nh mục",
              padding: EdgeInsets.symmetric(horizontal: 16, vertical: 2),
            ),
          ),
        ],
      ),
    );
  }
}
