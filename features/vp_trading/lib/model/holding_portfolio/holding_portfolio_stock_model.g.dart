// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'holding_portfolio_stock_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

HoldingPortfolioStockModel _$HoldingPortfolioStockModelFromJson(
  Map<String, dynamic> json,
) => HoldingPortfolioStockModel(
  custodyCd: json['custodyCd'] as String?,
  accountId: json['accountId'] as String?,
  symbol: json['symbol'] as String?,
  secType: json['secType'] as String?,
  market: json['market'] as String?,
  total: json['total'] as num?,
  trade: json['trade'] as num?,
  blocked: json['blocked'] as num?,
  vsdMortgage: json['vsdMortgage'] as num?,
  mortgage: json['mortgage'] as num?,
  restrict: json['restrict'] as num?,
  receivingRight: json['receivingRight'] as num?,
  receivingT0: json['receivingT0'] as num?,
  receivingT1: json['receivingT1'] as num?,
  receivingT2: json['receivingT2'] as num?,
  costPrice: (json['costPrice'] as num?)?.toDouble(),
  costPriceAmt: (json['costPriceAmt'] as num?)?.toDouble(),
  basicPrice: json['basicPrice'] as num?,
  basicPriceAmt: json['basicPriceAmt'] as num?,
  marginAmt: json['marginAmt'] as String?,
  pnlAmt: json['pnlAmt'] as String?,
  pnlRate: json['pnlRate'] as String?,
  isSell: json['isSell'] as String?,
  withdraw: json['withdraw'] as num?,
  matchIngAmt: json['matchIngAmt'] as num?,
  totalPnl: json['totalPnl'] as num?,
  productTypeName: json['productTypeName'] as String?,
  marketPrice: json['marketPrice'] as num?,
  ceilingPrice: json['ceilingPrice'] as num?,
  floorPrice: json['floorPrice'] as num?,
  refPrice: json['refPrice'] as num?,
  formatPrice: json['formatPrice'] as num?,
);

Map<String, dynamic> _$HoldingPortfolioStockModelToJson(
  HoldingPortfolioStockModel instance,
) => <String, dynamic>{
  'custodyCd': instance.custodyCd,
  'accountId': instance.accountId,
  'symbol': instance.symbol,
  'secType': instance.secType,
  'market': instance.market,
  'total': instance.total,
  'trade': instance.trade,
  'blocked': instance.blocked,
  'vsdMortgage': instance.vsdMortgage,
  'mortgage': instance.mortgage,
  'restrict': instance.restrict,
  'receivingRight': instance.receivingRight,
  'receivingT0': instance.receivingT0,
  'receivingT1': instance.receivingT1,
  'receivingT2': instance.receivingT2,
  'costPrice': instance.costPrice,
  'costPriceAmt': instance.costPriceAmt,
  'basicPrice': instance.basicPrice,
  'basicPriceAmt': instance.basicPriceAmt,
  'marginAmt': instance.marginAmt,
  'pnlAmt': instance.pnlAmt,
  'pnlRate': instance.pnlRate,
  'isSell': instance.isSell,
  'withdraw': instance.withdraw,
  'matchIngAmt': instance.matchIngAmt,
  'totalPnl': instance.totalPnl,
  'productTypeName': instance.productTypeName,
  'marketPrice': instance.marketPrice,
  'ceilingPrice': instance.ceilingPrice,
  'floorPrice': instance.floorPrice,
  'refPrice': instance.refPrice,
};
