// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'sell_portfolio_request_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SellPortfolioOrderRequest _$SellPortfolioOrderRequestFromJson(
  Map<String, dynamic> json,
) => SellPortfolioOrderRequest(
  symbol: json['symbol'] as String,
  quantity: (json['quantity'] as num).toInt(),
  price: (json['price'] as num).toDouble(),
  priceType: json['priceType'] as String,
  accountId: json['accountId'] as String,
);

Map<String, dynamic> _$SellPortfolioOrderRequestToJson(
  SellPortfolioOrderRequest instance,
) => <String, dynamic>{
  'symbol': instance.symbol,
  'quantity': instance.quantity,
  'price': instance.price,
  'priceType': instance.priceType,
  'accountId': instance.accountId,
};

SellPortfolioRequest _$SellPortfolioRequestFromJson(
  Map<String, dynamic> json,
) => SellPortfolioRequest(
  orders:
      (json['orders'] as List<dynamic>)
          .map(
            (e) =>
                SellPortfolioOrderRequest.fromJson(e as Map<String, dynamic>),
          )
          .toList(),
  accountType: json['accountType'] as String,
);

Map<String, dynamic> _$SellPortfolioRequestToJson(
  SellPortfolioRequest instance,
) => <String, dynamic>{
  'orders': instance.orders,
  'accountType': instance.accountType,
};
