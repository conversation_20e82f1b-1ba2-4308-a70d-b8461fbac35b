import 'package:json_annotation/json_annotation.dart';

part 'sell_portfolio_request_model.g.dart';

@JsonSerializable()
class SellPortfolioOrderRequest {
  final String symbol;
  final int quantity;
  final double price;
  final String priceType; // "LO" or "MP"
  final String accountId;

  const SellPortfolioOrderRequest({
    required this.symbol,
    required this.quantity,
    required this.price,
    required this.priceType,
    required this.accountId,
  });

  factory SellPortfolioOrderRequest.fromJson(Map<String, dynamic> json) =>
      _$SellPortfolioOrderRequestFromJson(json);

  Map<String, dynamic> toJson() => _$SellPortfolioOrderRequestToJson(this);
}

@JsonSerializable()
class SellPortfolioRequest {
  final List<SellPortfolioOrderRequest> orders;
  final String accountType; // "NORMAL" or "MARGIN"

  const SellPortfolioRequest({required this.orders, required this.accountType});

  factory SellPortfolioRequest.fromJson(Map<String, dynamic> json) =>
      _$SellPortfolioRequestFromJson(json);

  Map<String, dynamic> toJson() => _$SellPortfolioRequestToJson(this);
}
