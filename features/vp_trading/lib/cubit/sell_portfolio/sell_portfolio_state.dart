import 'package:equatable/equatable.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_trading/model/holding_portfolio/holding_portfolio_stock_model.dart';

enum PriceType { lo, market }

enum AccountType { normal, margin }

class SellPortfolioStockItem extends Equatable {
  final HoldingPortfolioStockModel stock;
  final bool isSelected;
  final int quantity;
  final double price;
  final PriceType priceType;

  const SellPortfolioStockItem({
    required this.stock,
    this.isSelected = false,
    this.quantity = 0,
    this.price = 0.0,
    this.priceType = PriceType.lo,
  });

  SellPortfolioStockItem copyWith({
    HoldingPortfolioStockModel? stock,
    bool? isSelected,
    int? quantity,
    double? price,
    PriceType? priceType,
  }) {
    return SellPortfolioStockItem(
      stock: stock ?? this.stock,
      isSelected: isSelected ?? this.isSelected,
      quantity: quantity ?? this.quantity,
      price: price ?? this.price,
      priceType: priceType ?? this.priceType,
    );
  }

  @override
  List<Object?> get props => [stock, isSelected, quantity, price, priceType];
}

class SellPortfolioState extends Equatable {
  final ApiStatus apiStatus;
  final AccountType accountType;
  final SubAccountModel? subAccount;
  final String searchQuery;
  final List<SellPortfolioStockItem> stockItems;
  final bool isSelectAll;
  final PriceType globalPriceType;
  final int globalQuantityPercent;
  final String? errorMessage;

  const SellPortfolioState({
    required this.apiStatus,
    this.accountType = AccountType.normal,
    this.subAccount,
    this.searchQuery = '',
    this.stockItems = const [],
    this.isSelectAll = false,
    this.globalPriceType = PriceType.lo,
    this.globalQuantityPercent = 100,
    this.errorMessage,
  });

  SellPortfolioState copyWith({
    ApiStatus? apiStatus,
    AccountType? accountType,
    SubAccountModel? subAccount,
    String? searchQuery,
    List<SellPortfolioStockItem>? stockItems,
    bool? isSelectAll,
    PriceType? globalPriceType,
    int? globalQuantityPercent,
    String? errorMessage,
  }) {
    return SellPortfolioState(
      apiStatus: apiStatus ?? this.apiStatus,
      accountType: accountType ?? this.accountType,
      subAccount: subAccount ?? this.subAccount,
      searchQuery: searchQuery ?? this.searchQuery,
      stockItems: stockItems ?? this.stockItems,
      isSelectAll: isSelectAll ?? this.isSelectAll,
      globalPriceType: globalPriceType ?? this.globalPriceType,
      globalQuantityPercent:
          globalQuantityPercent ?? this.globalQuantityPercent,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  List<SellPortfolioStockItem> get filteredStockItems {
    if (searchQuery.isEmpty) return stockItems;
    return stockItems
        .where(
          (item) => (item.stock.symbol ?? '').toLowerCase().contains(
            searchQuery.toLowerCase(),
          ),
        )
        .toList();
  }

  List<SellPortfolioStockItem> get selectedStockItems {
    return stockItems.where((item) => item.isSelected).toList();
  }

  int get selectedStockCount {
    return selectedStockItems.length;
  }

  bool get canSubmit {
    return selectedStockItems.isNotEmpty &&
        selectedStockItems.every(
          (item) =>
              item.quantity > 0 &&
              item.price > 0 &&
              item.quantity <= (item.stock.trade ?? 0),
        );
  }

  @override
  List<Object?> get props => [
    apiStatus,
    accountType,
    subAccount,
    searchQuery,
    stockItems,
    isSelectAll,
    globalPriceType,
    globalQuantityPercent,
    errorMessage,
  ];
}
