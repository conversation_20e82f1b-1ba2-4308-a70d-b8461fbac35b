import 'package:vp_core/vp_core.dart';
import 'package:vp_trading/core/repository/holding_portfolio_repository.dart';
import 'package:vp_trading/model/holding_portfolio/holding_portfolio_stock_model.dart';

import 'sell_portfolio_state.dart';

class SellPortfolioCubit extends Cubit<SellPortfolioState> {
  final HoldingPortfolioRepository _repository =
      GetIt.instance<HoldingPortfolioRepository>();

  SellPortfolioCubit()
    : super(SellPortfolioState(apiStatus: ApiStatus.initial()));

  void updateAccountType(AccountType accountType) {
    emit(state.copyWith(accountType: accountType));
    fetchHoldingPortfolio();
  }

  void updateSubAccount(SubAccountModel? subAccount) {
    emit(state.copyWith(subAccount: subAccount));
    fetchHoldingPortfolio();
  }

  void updateSearchQuery(String query) {
    emit(state.copyWith(searchQuery: query));
  }

  void toggleSelectAll() {
    final newSelectAll = !state.isSelectAll;
    final updatedItems =
        state.stockItems.map((item) {
          return item.copyWith(isSelected: newSelectAll);
        }).toList();

    emit(state.copyWith(isSelectAll: newSelectAll, stockItems: updatedItems));
  }

  void toggleStockSelection(String symbol) {
    final updatedItems =
        state.stockItems.map((item) {
          if (item.stock.symbol == symbol) {
            return item.copyWith(isSelected: !item.isSelected);
          }
          return item;
        }).toList();

    final allSelected = updatedItems.every((item) => item.isSelected);

    emit(state.copyWith(stockItems: updatedItems, isSelectAll: allSelected));
  }

  void updateStockQuantity(String symbol, int quantity) {
    final updatedItems =
        state.stockItems.map((item) {
          if (item.stock.symbol == symbol) {
            return item.copyWith(quantity: quantity);
          }
          return item;
        }).toList();

    emit(state.copyWith(stockItems: updatedItems));
  }

  void updateStockPrice(String symbol, double price) {
    final updatedItems =
        state.stockItems.map((item) {
          if (item.stock.symbol == symbol) {
            return item.copyWith(price: price);
          }
          return item;
        }).toList();

    emit(state.copyWith(stockItems: updatedItems));
  }

  void updateGlobalQuantityPercent(int percent) {
    emit(state.copyWith(globalQuantityPercent: percent));
    _applyGlobalQuantityPercent(percent);
  }

  void updateGlobalPriceType(PriceType priceType) {
    emit(state.copyWith(globalPriceType: priceType));
    _applyGlobalPriceType(priceType);
  }

  void _applyGlobalQuantityPercent(int percent) {
    final updatedItems =
        state.stockItems.map((item) {
          final maxQuantity = item.stock.trade ?? 0;
          final newQuantity = ((maxQuantity * percent) / 100).round();
          return item.copyWith(quantity: newQuantity);
        }).toList();

    emit(state.copyWith(stockItems: updatedItems));
  }

  void _applyGlobalPriceType(PriceType priceType) {
    final updatedItems =
        state.stockItems.map((item) {
          double newPrice;
          if (priceType == PriceType.lo) {
            newPrice = item.stock.floorPrice?.toDouble() ?? 0.0;
          } else {
            newPrice =
                item.stock.marketPrice?.toDouble() ??
                item.stock.refPrice?.toDouble() ??
                item.stock.basicPrice?.toDouble() ??
                0.0;
          }
          return item.copyWith(price: newPrice, priceType: priceType);
        }).toList();

    emit(state.copyWith(stockItems: updatedItems));
  }

  Future<void> fetchHoldingPortfolio() async {
    emit(state.copyWith(apiStatus: ApiStatus.loading()));
    try {
      final List<HoldingPortfolioStockModel>? portfolioList = await _repository
          .getHoldingPortfolio(accountId: state.subAccount?.id, symbol: "ALL");

      final stockItems =
          portfolioList?.map((stock) {
            return SellPortfolioStockItem(
              stock: stock,
              price: stock.floorPrice?.toDouble() ?? 0.0,
              priceType: state.globalPriceType,
            );
          }).toList() ??
          [];

      emit(
        state.copyWith(
          apiStatus: ApiStatus.done(),
          stockItems: stockItems,
          isSelectAll: false,
        ),
      );
    } catch (error) {
      emit(
        state.copyWith(
          apiStatus: ApiStatus.error(error.toString()),
          errorMessage: error.toString(),
        ),
      );
    }
  }

  Future<void> submitSellOrders() async {
    if (!state.canSubmit) return;

    emit(state.copyWith(apiStatus: ApiStatus.loading()));
    try {
      // TODO: Implement actual sell order submission
      // This would call the appropriate repository method to submit orders

      emit(state.copyWith(apiStatus: ApiStatus.done()));
    } catch (error) {
      emit(
        state.copyWith(
          apiStatus: ApiStatus.error(error.toString()),
          errorMessage: error.toString(),
        ),
      );
    }
  }
}
