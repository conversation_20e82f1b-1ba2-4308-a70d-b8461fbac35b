name: vp_auth
description: A starting point for Dart libraries or applications.
version: 1.0.0
# repository: https://github.com/my_org/my_repo

environment:
  sdk: ^3.3.4

# Add regular dependencies here.
dependencies:
  # path: ^1.8.0
  flutter_bloc:
  flutter:
    sdk: flutter
  local_auth:
  permission_handler:
  sms_autofill:
  vp_design_system:
    path: ../../library/vp_design_system
  vp_common:
    path: ../../library/vp_common
  vp_core:
    path: ../../library/vp_core
  pin_code_fields:
  recaptcha_enterprise_flutter: 18.5.1
  local_auth_darwin:
  camera:
  image:
  # ekyc_plugin_flutter:
  #   path: ../../plugins/flutter_ekyc_sdk
dev_dependencies:
  # lints: ^3.0.0
  # test: ^1.24.0
   build_runner:
   custom_lint:
   json_serializable:
   retrofit_generator:
   flutter_gen_runner:
   intl_utils:
dependency_overrides:
   flutter_svg: 2.0.10
   intl: ^0.19.0
   collection: ^1.19.1
flutter:
  uses-material-design: true
  assets:
    - assets/images/
    - assets/icons/
flutter_intl:
  enabled: true