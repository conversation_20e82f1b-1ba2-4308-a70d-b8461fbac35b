---
type: "always_apply"
---


# VP Utility Module - Quy tắc Style Code

## 1. C<PERSON>u trúc thư mục
```
lib/
├── core/           # Logic nghiệp vụ cốt lõi
├── cubit/          # State management
├── generated/      # File được generate tự động
├── l10n/          # Đa ngôn ngữ
├── model/         # Models/DTOs
├── router/        # Định tuyến
├── screen/        # UI screens
├── vp_utility.dart     # Entry point
└── vp_utility_module.dart  # Module configuration
```

## 2. Quy tắc đặt tên

### 2.1. File
- Sử dụng snake_case: `user_profile_screen.dart`
- Tên file phải mô tả rõ chức năng
- Các file test phải có hậu tố `_test`: `user_profile_test.dart`
- File model phải có hậu tố `_model`: `user_model.dart`
- File cubit phải có hậu tố `_cubit`: `profile_cubit.dart`
- File state phải có hậu tố `_state`: `profile_state.dart`

### 2.2. Class
- Sử dụng PascalCase: `UserProfileScreen`
- Tên class phải rõ ràng và đầy đủ nghĩa
- Widget class phải có hậu tố phù hợp:
  - Screen/Page: `UserProfileScreen`
  - Component: `UserAvatarWidget`
  - Item: `UserListItem`
- Model class phải có hậu tố `Model`: `UserModel`
- Cubit class phải có hậu tố `Cubit`: `ProfileCubit`
- State class phải có hậu tố `State`: `ProfileState`

### 2.3. Biến và hàm
- Sử dụng camelCase: `userName`, `getUserProfile()`
- Tên biến phải mô tả được giá trị chứa bên trong
- Tên hàm nên bắt đầu bằng động từ: `fetchData()`, `updateProfile()`
- Callback nên bắt đầu bằng `on`: `onPressed`, `onUserUpdated`

## 3. Style Code

### 3.1. Chung
- Giới hạn độ dài mỗi dòng: 80 ký tự
- Sử dụng 2 spaces cho indentation
- Luôn có dấu `;` ở cuối mỗi statement
- Sử dụng dấu ngoặc nhọn cho tất cả control flow statements

### 3.2. Import/Export
- Sắp xếp import theo thứ tự:
  1. Dart SDK imports
  2. Flutter imports
  3. Third party packages
  4. Project imports
- Phân tách các nhóm import bằng dòng trống
```dart
import 'dart:async';

import 'package:flutter/material.dart';

import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';

import '../model/user_model.dart';
```

### 3.3. Classes
- Fields theo thứ tự:
  1. Static fields
  2. Instance fields
  3. Constructors
  4. Static methods
  5. Instance methods
- Phân tách các nhóm bằng dòng trống
- Private fields/methods bắt đầu bằng underscore

### 3.4. Widget
- Chia nhỏ widget phức tạp thành các component
- Sử dụng const constructor khi có thể
- Tách logic ra khỏi widget, đưa vào cubit/bloc
- Sử dụng extension methods cho widget styling
```dart
class UserProfileScreen extends StatelessWidget {
  const UserProfileScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: _buildBody(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    // ...
  }

  Widget _buildBody() {
    // ...
  }
}
```

### 3.5. State Management
- Sử dụng Cubit cho state management đơn giản
- Tách state thành các class riêng biệt
- Đặt tên state theo format: Initial, Loading, Success, Error
```dart
abstract class ProfileState extends Equatable {
  const ProfileState();
}

class ProfileInitial extends ProfileState {
  const ProfileInitial();
  
  @override
  List<Object> get props => [];
}
```

### 3.6. Models
- Sử dụng freezed cho data classes
- Implement Equatable cho so sánh object
- Có các method toJson() và fromJson()
- Tách validation logic vào các method riêng

### 3.7. Testing
- Mỗi file cần có test tương ứng
- Đặt tên test theo format: "should ... when ..."
- Sử dụng given/when/then pattern
- Mock tất cả external dependencies

## 4. Documentation
- Thêm documentation cho public API
- Sử dụng /// cho documentation comments
- Mô tả params và return value
- Thêm example code khi cần thiết
```dart
/// Fetches user profile from the server.
/// 
/// [userId] must be a valid UUID.
/// Throws [AuthException] if user is not authenticated.
/// 
/// Example:
/// ```dart
/// final profile = await fetchUserProfile('123');
/// ```
Future<UserProfile> fetchUserProfile(String userId) async {
  // ...
}
```

## 5. Error Handling
- Sử dụng custom Exception classes
- Xử lý tất cả các error cases
- Hiển thị user-friendly error messages
- Log errors với đầy đủ context

## 6. Performance
- Sử dụng const constructors
- Tránh rebuild không cần thiết
- Cache network responses
- Sử dụng indexed_db cho local storage lớn
- Tối ưu image loading và caching

## 7. Sử dụng Common UI từ VP Design System

### 7.1. Nguyên tắc chung
- Luôn ưu tiên sử dụng components có sẵn từ VP Design System
- KHÔNG tự tạo components mới nếu đã có trong Design System
- KHÔNG chỉnh sửa trực tiếp style của components từ Design System
- Sử dụng theme và colors từ Design System

### 7.2. Import Components
```dart
// ✓ ĐÚNG: Import cụ thể những components cần dùng
import 'package:vp_design_system/widget/button/vp_button.dart';
import 'package:vp_design_system/widget/text_field/vp_text_field.dart';

// ✗ SAI: Import toàn bộ package
import 'package:vp_design_system/vp_design_system.dart';
```

### 7.3. Buttons
- Sử dụng `VPButton` cho tất cả các buttons trong app
- Chọn đúng variant phù hợp với context:
```dart
// Primary button cho action chính
VPButton.primary(
  onPressed: () => {},
  text: 'Xác nhận',
)

// Secondary button cho action phụ
VPButton.secondary(
  onPressed: () => {},
  text: 'Hủy',
)

// Text button cho links hoặc actions nhẹ
VPButton.text(
  onPressed: () => {},
  text: 'Xem thêm',
)
```

### 7.4. Text Fields
- Sử dụng `VPTextField` cho tất cả input fields
- Luôn có validation và error handling:
```dart
VPTextField(
  controller: _controller,
  label: 'Email',
  validator: (value) {
    if (!value.isValidEmail) {
      return 'Email không hợp lệ';
    }
    return null;
  },
  errorText: state.emailError,
)
```

### 7.5. Typography
- Sử dụng các text styles có sẵn từ theme
- KHÔNG tự định nghĩa font sizes hoặc weights
```dart
Text(
  'Tiêu đề',
  style: context.textTheme.titleLarge,
)

Text(
  'Nội dung',
  style: context.textTheme.bodyMedium,
)
```

### 7.6. Colors & Theme
- Sử dụng colors từ theme, KHÔNG hardcode màu
- Sử dụng semantic colors thay vì direct colors
```dart
// ✓ ĐÚNG: Sử dụng semantic colors
Container(
  color: context.colors.background,
  child: Text(
    'Content',
    style: TextStyle(
      color: context.colors.textPrimary,
    ),
  ),
)

// ✗ SAI: Hardcode colors
Container(
  color: Colors.white,
  child: Text(
    'Content',
    style: TextStyle(
      color: Colors.black,
    ),
  ),
)
```

### 7.7. Icons
- Sử dụng icons từ Design System assets
- Đặt tên biến icon theo chuẩn:
```dart
// ✓ ĐÚNG: Sử dụng icons từ Design System
DesignAssets.icons.icHome.svg(
  width: 24,
  height: 24,
  color: context.colors.iconPrimary,
)

// ✗ SAI: Sử dụng Icons từ material
Icon(Icons.home)
```

### 7.8. Spacing & Layout
- Sử dụng các constant spacing từ theme
- Áp dụng spacing một cách nhất quán:
```dart
Padding(
  padding: EdgeInsets.all(Spacings.s16),
  child: Column(
    children: [
      Text('Title'),
      SizedBox(height: Spacings.s8),
      Text('Content'),
    ],
  ),
)
```

### 7.9. Custom Widgets
- Chỉ tạo custom widget khi thực sự cần thiết
- Tuân thủ style guide của Design System
- Tài liệu hóa rõ ràng lý do không sử dụng component có sẵn
- Đề xuất thêm vào Design System nếu widget có tính tái sử dụng cao

### 7.10. Responsive Design
- Sử dụng các breakpoints từ Design System
- Áp dụng responsive design một cách nhất quán:
```dart
// Sử dụng extension methods cho responsive
Container(
  width: context.responsive(
    mobile: double.infinity,
    tablet: 400,
    desktop: 600,
  ),
)
```

### 7.11. Forms
- Sử dụng `VPForm` cho tất cả các forms
- Implement validation đầy đủ
- Xử lý loading và error states:
```dart
VPForm(
  child: Column(
    children: [
      VPTextField(...),
      VPDropdown(...),
      VPButton(
        loading: state.isSubmitting,
        onPressed: _handleSubmit,
      ),
    ],
  ),
)
```

### 7.12. Loading & Error States
- Sử dụng `VPLoadingIndicator` cho loading states
- Sử dụng `VPErrorWidget` cho error states
- Hiển thị skeleton loading khi cần:
```dart
if (state.isLoading) {
  return VPLoadingIndicator();
} else if (state.hasError) {
  return VPErrorWidget(
    message: state.errorMessage,
    onRetry: _handleRetry,
  );
}
```

### 7.13. Animations
- Sử dụng các animation presets từ Design System
- Giữ animations nhất quán trong toàn app:
```dart
// Animation cho transitions
VPPageTransition(
  child: NextScreen(),
)

// Animation cho loading
VPLoadingSpinner(
  size: LoadingSize.medium,
)
```
# VP Utility Module - Quy tắc Style Code

## 1. Cấu trúc thư mục
```
lib/
├── core/           # Logic nghiệp vụ cốt lõi
├── cubit/          # State management
├── generated/      # File được generate tự động
├── l10n/          # Đa ngôn ngữ
├── model/         # Models/DTOs
├── router/        # Định tuyến
├── screen/        # UI screens
├── vp_utility.dart     # Entry point
└── vp_utility_module.dart  # Module configuration
```

## 2. Quy tắc đặt tên

### 2.1. File
- Sử dụng snake_case: `user_profile_screen.dart`
- Tên file phải mô tả rõ chức năng
- Các file test phải có hậu tố `_test`: `user_profile_test.dart`
- File model phải có hậu tố `_model`: `user_model.dart`
- File cubit phải có hậu tố `_cubit`: `profile_cubit.dart`
- File state phải có hậu tố `_state`: `profile_state.dart`

### 2.2. Class
- Sử dụng PascalCase: `UserProfileScreen`
- Tên class phải rõ ràng và đầy đủ nghĩa
- Widget class phải có hậu tố phù hợp:
  - Screen/Page: `UserProfileScreen`
  - Component: `UserAvatarWidget`
  - Item: `UserListItem`
- Model class phải có hậu tố `Model`: `UserModel`
- Cubit class phải có hậu tố `Cubit`: `ProfileCubit`
- State class phải có hậu tố `State`: `ProfileState`

### 2.3. Biến và hàm
- Sử dụng camelCase: `userName`, `getUserProfile()`
- Tên biến phải mô tả được giá trị chứa bên trong
- Tên hàm nên bắt đầu bằng động từ: `fetchData()`, `updateProfile()`
- Callback nên bắt đầu bằng `on`: `onPressed`, `onUserUpdated`

## 3. Style Code

### 3.1. Chung
- Giới hạn độ dài mỗi dòng: 80 ký tự
- Sử dụng 2 spaces cho indentation
- Luôn có dấu `;` ở cuối mỗi statement
- Sử dụng dấu ngoặc nhọn cho tất cả control flow statements

### 3.2. Import/Export
- Sắp xếp import theo thứ tự:
  1. Dart SDK imports
  2. Flutter imports
  3. Third party packages
  4. Project imports
- Phân tách các nhóm import bằng dòng trống
```dart
import 'dart:async';

import 'package:flutter/material.dart';

import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';

import '../model/user_model.dart';
```

### 3.3. Classes
- Fields theo thứ tự:
  1. Static fields
  2. Instance fields
  3. Constructors
  4. Static methods
  5. Instance methods
- Phân tách các nhóm bằng dòng trống
- Private fields/methods bắt đầu bằng underscore

### 3.4. Widget
- Chia nhỏ widget phức tạp thành các component
- Sử dụng const constructor khi có thể
- Tách logic ra khỏi widget, đưa vào cubit/bloc
- Sử dụng extension methods cho widget styling
```dart
class UserProfileScreen extends StatelessWidget {
  const UserProfileScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: _buildBody(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    // ...
  }

  Widget _buildBody() {
    // ...
  }
}
```

### 3.5. State Management
- Sử dụng Cubit cho state management đơn giản
- Tách state thành các class riêng biệt
- Đặt tên state theo format: Initial, Loading, Success, Error
```dart
abstract class ProfileState extends Equatable {
  const ProfileState();
}

class ProfileInitial extends ProfileState {
  const ProfileInitial();
  
  @override
  List<Object> get props => [];
}
```

### 3.6. Models
- Sử dụng freezed cho data classes
- Implement Equatable cho so sánh object
- Có các method toJson() và fromJson()
- Tách validation logic vào các method riêng

### 3.7. Testing
- Mỗi file cần có test tương ứng
- Đặt tên test theo format: "should ... when ..."
- Sử dụng given/when/then pattern
- Mock tất cả external dependencies

## 4. Documentation
- Thêm documentation cho public API
- Sử dụng /// cho documentation comments
- Mô tả params và return value
- Thêm example code khi cần thiết
```dart
/// Fetches user profile from the server.
/// 
/// [userId] must be a valid UUID.
/// Throws [AuthException] if user is not authenticated.
/// 
/// Example:
/// ```dart
/// final profile = await fetchUserProfile('123');
/// ```
Future<UserProfile> fetchUserProfile(String userId) async {
  // ...
}
```

## 5. Error Handling
- Sử dụng custom Exception classes
- Xử lý tất cả các error cases
- Hiển thị user-friendly error messages
- Log errors với đầy đủ context

## 6. Performance
- Sử dụng const constructors
- Tránh rebuild không cần thiết
- Cache network responses
- Sử dụng indexed_db cho local storage lớn
- Tối ưu image loading và caching

## 7. Sử dụng Common UI từ VP Design System

### 7.1. Nguyên tắc chung
- Luôn ưu tiên sử dụng components có sẵn từ VP Design System
- KHÔNG tự tạo components mới nếu đã có trong Design System
- KHÔNG chỉnh sửa trực tiếp style của components từ Design System
- Sử dụng theme và colors từ Design System

### 7.2. Import Components
```dart
// ✓ ĐÚNG: Import cụ thể những components cần dùng
import 'package:vp_design_system/widget/button/vp_button.dart';
import 'package:vp_design_system/widget/text_field/vp_text_field.dart';

// ✗ SAI: Import toàn bộ package
import 'package:vp_design_system/vp_design_system.dart';
```

### 7.3. Buttons
- Sử dụng `VPButton` cho tất cả các buttons trong app
- Chọn đúng variant phù hợp với context:
```dart
// Primary button cho action chính
VPButton.primary(
  onPressed: () => {},
  text: 'Xác nhận',
)

// Secondary button cho action phụ
VPButton.secondary(
  onPressed: () => {},
  text: 'Hủy',
)

// Text button cho links hoặc actions nhẹ
VPButton.text(
  onPressed: () => {},
  text: 'Xem thêm',
)
```

### 7.4. Text Fields
- Sử dụng `VPTextField` cho tất cả input fields
- Luôn có validation và error handling:
```dart
VPTextField(
  controller: _controller,
  label: 'Email',
  validator: (value) {
    if (!value.isValidEmail) {
      return 'Email không hợp lệ';
    }
    return null;
  },
  errorText: state.emailError,
)
```

### 7.5. Typography
- Sử dụng các text styles có sẵn từ theme
- KHÔNG tự định nghĩa font sizes hoặc weights
```dart
Text(
  'Tiêu đề',
  style: context.textTheme.titleLarge,
)

Text(
  'Nội dung',
  style: context.textTheme.bodyMedium,
)
```

### 7.6. Colors & Theme
- Sử dụng colors từ theme, KHÔNG hardcode màu
- Sử dụng semantic colors thay vì direct colors
```dart
// ✓ ĐÚNG: Sử dụng semantic colors
Container(
  color: context.colors.background,
  child: Text(
    'Content',
    style: TextStyle(
      color: context.colors.textPrimary,
    ),
  ),
)

// ✗ SAI: Hardcode colors
Container(
  color: Colors.white,
  child: Text(
    'Content',
    style: TextStyle(
      color: Colors.black,
    ),
  ),
)
```

### 7.7. Icons
- Sử dụng icons từ Design System assets
- Đặt tên biến icon theo chuẩn:
```dart
// ✓ ĐÚNG: Sử dụng icons từ Design System
DesignAssets.icons.icHome.svg(
  width: 24,
  height: 24,
  color: context.colors.iconPrimary,
)

// ✗ SAI: Sử dụng Icons từ material
Icon(Icons.home)
```

### 7.8. Spacing & Layout
- Sử dụng các constant spacing từ theme
- Áp dụng spacing một cách nhất quán:
```dart
Padding(
  padding: EdgeInsets.all(Spacings.s16),
  child: Column(
    children: [
      Text('Title'),
      SizedBox(height: Spacings.s8),
      Text('Content'),
    ],
  ),
)
```

### 7.9. Custom Widgets
- Chỉ tạo custom widget khi thực sự cần thiết
- Tuân thủ style guide của Design System
- Tài liệu hóa rõ ràng lý do không sử dụng component có sẵn
- Đề xuất thêm vào Design System nếu widget có tính tái sử dụng cao

### 7.10. Responsive Design
- Sử dụng các breakpoints từ Design System
- Áp dụng responsive design một cách nhất quán:
```dart
// Sử dụng extension methods cho responsive
Container(
  width: context.responsive(
    mobile: double.infinity,
    tablet: 400,
    desktop: 600,
  ),
)
```

### 7.11. Forms
- Sử dụng `VPForm` cho tất cả các forms
- Implement validation đầy đủ
- Xử lý loading và error states:
```dart
VPForm(
  child: Column(
    children: [
      VPTextField(...),
      VPDropdown(...),
      VPButton(
        loading: state.isSubmitting,
        onPressed: _handleSubmit,
      ),
    ],
  ),
)
```

### 7.12. Loading & Error States
- Sử dụng `VPLoadingIndicator` cho loading states
- Sử dụng `VPErrorWidget` cho error states
- Hiển thị skeleton loading khi cần:
```dart
if (state.isLoading) {
  return VPLoadingIndicator();
} else if (state.hasError) {
  return VPErrorWidget(
    message: state.errorMessage,
    onRetry: _handleRetry,
  );
}
```

### 7.13. Animations
- Sử dụng các animation presets từ Design System
- Giữ animations nhất quán trong toàn app:
```dart
// Animation cho transitions
VPPageTransition(
  child: NextScreen(),
)

// Animation cho loading
VPLoadingSpinner(
  size: LoadingSize.medium,
)
```
